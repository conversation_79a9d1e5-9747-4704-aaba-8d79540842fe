# Lab 4 MongoDB Queries - Complete List

## Setup Commands
```javascript
// Connect to database
use laptopStore

// Create text index for text search (run this before text search queries)
db.laptops.createIndex({"description": "text"})
```

## Comparison Queries (Complete 4 of 6)

### Query 1: Find laptops with price equal to $999.99
```javascript
db.laptops.find({"price": 999.99})
```

### Query 2: Find laptops with prices not equal to $1299.99
```javascript
db.laptops.find({"price": {$ne: 1299.99}})
```

### Query 3: Find laptops with price greater than $1500
```javascript
db.laptops.find({"price": {$gt: 1500}})
```

### Query 4: Find laptops with price greater than or equal to $1399.99
```javascript
db.laptops.find({"price": {$gte: 1399.99}})
```

## Logical Queries (Complete 3 of 4)

### Query 5: Find laptops with price less than $1000 OR stock greater than 10
```javascript
db.laptops.find({$or: [{"price": {$lt: 1000}}, {"stock": {$gt: 10}}]})
```

### Query 6: Find laptops with price less than $1000 AND stock greater than 10
```javascript
db.laptops.find({$and: [{"price": {$lt: 1000}}, {"stock": {$gt: 10}}]})
```

### Query 7: Find laptops with price NOT equal to $999.99
```javascript
db.laptops.find({"price": {$not: {$eq: 999.99}}})
```

## Element Operators (Complete 2 of 2)

### Query 8: Find laptops with "processor" field using exists
```javascript
db.laptops.find({"processor": {$exists: true}})
```

### Query 9: Find laptops with "reviews" field missing
```javascript
db.laptops.find({"reviews": {$exists: false}})
```

## Array Operators (Complete 2 of 2)
Note: Since the current data doesn't have genres arrays, we'll add some laptops with genres first

### Add laptops with genres for array queries:
```javascript
db.laptops.insertOne({"brand": "Gaming Corp", "model": "Gaming Laptop", "processor": "Intel Core i9", "ram": "32GB", "storage": "1TB SSD", "price": 2499.99, "stock": 2, "genres": ["Gaming", "High-Performance"]})
db.laptops.insertOne({"brand": "Business Inc", "model": "Business Pro", "processor": "Intel Core i5", "ram": "16GB", "storage": "512GB SSD", "price": 1599.99, "stock": 8, "genres": ["Business", "Professional"]})
```

### Query 10: Find laptops with genre "Gaming" in their genres array
```javascript
db.laptops.find({"genres": "Gaming"})
```

### Query 11: Find laptops with genre "Business" using $in
```javascript
db.laptops.find({"genres": {$in: ["Business"]}})
```

## Text Search Operators (Complete 1 of 1)
Note: Since the current data doesn't have description fields, we'll add one first

### Add laptop with description for text search:
```javascript
db.laptops.insertOne({"brand": "Performance Co", "model": "Performance Laptop", "processor": "Intel Core i7", "ram": "16GB", "storage": "512GB SSD", "price": 1399.99, "stock": 5, "description": "High performance laptop for professionals"})
db.laptops.createIndex({"description": "text"})
```

### Query 12: Perform text search for laptops with word "performance"
```javascript
db.laptops.find({$text: {$search: "performance"}})
```

## Array Update Operators (Complete 1 of 1)

### Query 13: Add new review to laptop with specific title
```javascript
db.laptops.updateOne(
  {"model": "MacBook Air"},
  {$push: {"reviews": {"user": "John Doe", "rating": 4}}}
)
```

### Query 14: Verify the update worked
```javascript
db.laptops.find({"model": "MacBook Air"})
```

## Summary
- **Total Queries**: 14 (including setup and verification)
- **Screenshot Requirements**: 13-14 screenshots (one for each query result)
- **Categories Covered**: All required categories with minimum requirements met
- **Points Available**: 30 marks total
