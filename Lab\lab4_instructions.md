# Lab 4: MongoDB and MongoSH Instructions

## Setup Instructions

### Step 1: Install MongoDB and MongoDB Compass
1. Download and install MongoDB Community Server
2. Download and install MongoDB Compass (GUI tool)
3. Start MongoDB service

### Step 2: Import Data into MongoDB
1. Open MongoDB Compass
2. Connect to your local MongoDB instance (usually `mongodb://localhost:27017`)
3. Create a new database called `laptopStore`
4. Create a new collection called `laptops`
5. Import the `lab4_laptops.json` file into the `laptops` collection

### Step 3: Open MongoDB Shell (mongosh)
1. Open Command Prompt or Terminal
2. Type `mongosh` to start MongoDB Shell
3. Connect to your database: `use laptopStore`

## Query Execution Guide

### Before Text Search Queries
For the text search query to work, you need to create a text index:
```javascript
db.laptops.createIndex({"description": "text"});
```

### Query Categories and Requirements

#### 1. Comparison Queries (Complete 4 of 6) - 8 marks total
- Find laptops with price equal to $999.99
- Find laptops with prices not equal to $1299.99  
- Find laptops with price greater than $1500
- Find laptops with price greater than or equal to $1399.99
- Find laptops with price less than $1000 (optional)
- Find laptops with price less than or equal to $1099.99 (optional)

#### 2. Logical Queries (Complete 3 of 4) - 6 marks total
- Find laptops with price less than $1000 OR stock greater than 10
- Find laptops with price less than $1000 AND stock greater than 10
- Find laptops with price NOT equal to $999.99
- Find laptops with price NOT equal to $999.99 NOR stock NOT equal to 8 (optional)

#### 3. Element Operators (Complete 2 of 2) - 4 marks total
- Find laptops with "description" field using exists
- Find laptops with "reviews" field missing

#### 4. Array Operators (Complete 2 of 2) - 4 marks total
- Find laptops with genre "Gaming" in their genres array
- Find laptops with genre "Business" using $in

#### 5. Text Search Operators (Complete 1 of 1) - 4 marks total
- Perform text search for laptops with word "performance"

#### 6. Array Update Operators (Complete 1 of 1) - 4 marks total
- Add new review to laptop with specific title

## Submission Requirements

1. **Screenshots**: Take 1 screenshot for each query result (up to 17 total)
2. **Query List**: Provide a list of all queries you executed in the order specified above
3. **No GitHub submission required**

## Grading Scheme
- Comparison: 8 marks (2 marks per query completed)
- Logical: 6 marks (2 marks per query completed)  
- Element: 4 marks (2 marks per query completed)
- Array: 4 marks (2 marks per query completed)
- Text Search: 4 marks
- Array Update: 4 marks
- **Total: 30 marks**
- **Overall Grade Weight: 4 marks**

## Tips for Success
1. Execute queries one at a time
2. Take clear screenshots showing both the query and results
3. Verify your data was imported correctly by running `db.laptops.find({})` first
4. Make sure to create the text index before running text search queries
5. For the update query, verify it worked by querying the updated document

## Sample Data Structure
Each laptop document contains:
- brand, model, processor, ram, storage, price, stock (required fields)
- genres (array field)
- description (text field for some documents)
- reviews (array field for some documents)
