// MongoDB Lab 4 Queries
// Modern Web Technologies - Lab 4: Using MongoDB and MongoSH

// First, you need to:
// 1. Import the lab4_laptops.json file into MongoDB Compass
// 2. Create a database called "laptopStore" 
// 3. Create a collection called "laptops"
// 4. Import the JSON data into the laptops collection

// Use the database
use('laptopStore');

// ===== COMPARISON QUERIES (Complete 4 of 6) =====

// 1. Find laptops with the price equal to $999.99
db.laptops.find({"price": 999.99});

// 2. Find laptops with prices not equal to $1299.99
db.laptops.find({"price": {$ne: 1299.99}});

// 3. Find laptops with a price greater than $1500
db.laptops.find({"price": {$gt: 1500}});

// 4. Find laptops with a price greater than or equal to $1399.99
db.laptops.find({"price": {$gte: 1399.99}});

// 5. Find laptops with a price less than $1000 (Optional - choose 4 of 6)
// db.laptops.find({"price": {$lt: 1000}});

// 6. Find laptops with a price less than or equal to $1099.99 (Optional - choose 4 of 6)
// db.laptops.find({"price": {$lte: 1099.99}});

// ===== LOGICAL QUERIES (Complete 3 of 4) =====

// 1. Find laptops with a price less than $1000 OR with a stock greater than 10
db.laptops.find({$or: [{"price": {$lt: 1000}}, {"stock": {$gt: 10}}]});

// 2. Find laptops with a price less than $1000 AND with a stock greater than 10
db.laptops.find({$and: [{"price": {$lt: 1000}}, {"stock": {$gt: 10}}]});

// 3. Find laptops with a price NOT equal to $999.99
db.laptops.find({"price": {$not: {$eq: 999.99}}});

// 4. Find laptops with a price NOT equal to $999.99 NOR with stock NOT equal to 8 (Optional - choose 3 of 4)
// db.laptops.find({$nor: [{"price": {$eq: 999.99}}, {"stock": {$eq: 8}}]});

// ===== ELEMENT OPERATORS (Complete 2 of 2) =====

// 1. Find laptops with the "description" field using exists
db.laptops.find({"description": {$exists: true}});

// 2. Find laptops with the "reviews" field missing
db.laptops.find({"reviews": {$exists: false}});

// ===== ARRAY OPERATORS (Complete 2 of 2) =====

// 1. Find laptops with a genre of "Gaming" in their genres array
db.laptops.find({"genres": "Gaming"});

// 2. Find laptops with the genre "Business" using $in
db.laptops.find({"genres": {$in: ["Business"]}});

// ===== TEXT SEARCH OPERATORS (Complete 1 of 1) =====
// Note: For text search to work, you need to create a text index first
// Create text index on description field:
// db.laptops.createIndex({"description": "text"});

// Perform a text search for laptops with the word "performance"
db.laptops.find({$text: {$search: "performance"}});

// ===== ARRAY UPDATE OPERATORS (Complete 1 of 1) =====

// Add a new review to a laptop with a specific title
// Example: Add review to "XPS 13" laptop
db.laptops.updateOne(
  {"model": "XPS 13"}, 
  {$push: {"reviews": {"user": "John Doe", "rating": 4}}}
);

// Verify the update worked
db.laptops.find({"model": "XPS 13"});
